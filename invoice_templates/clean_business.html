<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #ffffff;
            font-size: 14px;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 35px;
            padding-bottom: 20px;
            border-bottom: 2px solid #059669;
        }

        .company-logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            max-width: 160px;
            max-height: 60px;
        }

        .company-name {
            font-size: 22px;
            font-weight: 600;
            color: #059669;
        }

        .company-info {
            text-align: right;
            color: #6b7280;
        }

        .company-details {
            font-size: 13px;
            line-height: 1.5;
        }

        /* Invoice Title */
        .invoice-title {
            font-size: 28px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 35px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .invoice-title::after {
            content: '';
            flex: 1;
            height: 2px;
            background: linear-gradient(90deg, #059669, #10b981);
        }

        /* Client and Invoice Info Section */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 45px;
            gap: 30px;
        }

        .client-info, .invoice-meta {
            width: 48%;
            background: #f0fdf4;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #059669;
        }

        .section-title {
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #059669;
            margin-bottom: 12px;
        }

        .client-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .client-details, .invoice-details {
            color: #4b5563;
            font-size: 13px;
            line-height: 1.6;
        }

        .invoice-number {
            font-size: 16px;
            font-weight: 600;
            color: #059669;
            margin-bottom: 8px;
        }

        /* Line Items Table */
        .line-items {
            margin-bottom: 35px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #d1d5db;
        }

        .items-table th {
            background: #059669;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .items-table td {
            padding: 14px 12px;
            border-bottom: 1px solid #e5e7eb;
            color: #1f2937;
        }

        .items-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .items-table tr:last-child td {
            border-bottom: none;
        }

        .text-right {
            text-align: right;
        }

        .item-description {
            font-weight: 500;
        }

        /* Totals Section */
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 45px;
        }

        .totals {
            width: 300px;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px;
            font-size: 14px;
            border-bottom: 1px solid #f3f4f6;
        }

        .total-row:last-child {
            border-bottom: none;
        }

        .total-row.subtotal {
            color: #6b7280;
            background: #f9fafb;
        }

        .total-row.final {
            font-size: 16px;
            font-weight: 600;
            color: white;
            background: #059669;
        }

        /* Footer */
        .footer {
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            line-height: 1.6;
            background: #f0fdf4;
            padding: 20px;
            border-radius: 8px;
            border-top: 3px solid #059669;
        }

        .payment-terms {
            margin-bottom: 12px;
            font-weight: 600;
            color: #1f2937;
        }

        .footer-content {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 12px;
        }

        .footer-item {
            flex: 1;
            min-width: 150px;
        }

        .footer-label {
            font-weight: 600;
            color: #059669;
            display: block;
            margin-bottom: 4px;
        }

        /* Print Styles */
        @media print {
            body {
                font-size: 12px;
            }

            .invoice-container {
                padding: 20px;
                box-shadow: none;
            }

            .header {
                margin-bottom: 25px;
            }

            .invoice-title {
                font-size: 24px;
                margin-bottom: 25px;
            }

            .info-section {
                margin-bottom: 30px;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .invoice-container {
                padding: 20px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .company-info {
                text-align: center;
            }

            .invoice-title {
                font-size: 24px;
                text-align: center;
            }

            .invoice-title::after {
                display: none;
            }

            .info-section {
                flex-direction: column;
                gap: 20px;
            }

            .client-info, .invoice-meta {
                width: 100%;
            }

            .totals {
                width: 100%;
            }

            .footer-content {
                flex-direction: column;
                text-align: center;
            }

            .items-table th,
            .items-table td {
                padding: 10px 8px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <div class="company-logo-section">
                <div class="company-name">{{ company_name }}</div>
            </div>
            <div class="company-info">
                <div class="company-details">
                    {{ company_address_line_1 }}<br>
                    {{ company_city }}, {{ company_state_province }} {{ company_postal_code }}<br>
                    {{ company_country }}<br>
                    {{ company_phone }}<br>
                    {{ company_email }}<br>
                    {{ company_website }}
                </div>
            </div>
        </div>

        <!-- Invoice Title -->
        <h1 class="invoice-title">Invoice</h1>

        <!-- Client and Invoice Info -->
        <div class="info-section">
            <div class="client-info">
                <div class="section-title">Bill To</div>
                <div class="client-name">{{ client_company }}</div>
                <div class="client-details">
                    {{ client_address }}<br>
                    {{ contact_email }}<br>
                    {{ phone_number }}
                </div>
            </div>
            <div class="invoice-meta">
                <div class="section-title">Invoice Information</div>
                <div class="invoice-number">#{{ invoice_number }}</div>
                <div class="invoice-details">
                    <strong>Date:</strong> {{ bill_date }}<br>
                    <strong>Due Date:</strong> {{ due_date }}<br>
                    <strong>PO Number:</strong> {{ order_reference }}<br>
                    <strong>Terms:</strong> {{ payment_terms }}
                </div>
            </div>
        </div>

        <!-- Line Items -->
        <div class="line-items">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th class="text-right">Quantity</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {{#line_items}}
                    <tr>
                        <td class="item-description">{{ service_description }}</td>
                        <td class="text-right">{{ quantity }}</td>
                        <td class="text-right">${{ unit_rate }}</td>
                        <td class="text-right">${{ line_amount }}</td>
                    </tr>
                    {{/line_items}}
                    {{^line_items}}
                    <tr>
                        <td class="item-description">{{ service_description }}</td>
                        <td class="text-right">{{ quantity }}</td>
                        <td class="text-right">${{ unit_rate }}</td>
                        <td class="text-right">${{ line_amount }}</td>
                    </tr>
                    {{/line_items}}
                </tbody>
            </table>
        </div>

        <!-- Totals -->
        <div class="totals-section">
            <div class="totals">
                <div class="total-row subtotal">
                    <span>Subtotal:</span>
                    <span>${{ subtotal }}</span>
                </div>
                {{#total_tax}}
                <div class="total-row">
                    <span>Tax:</span>
                    <span>${{ total_tax }}</span>
                </div>
                {{/total_tax}}
                {{#total_discount}}
                <div class="total-row">
                    <span>Discount:</span>
                    <span>-${{ total_discount }}</span>
                </div>
                {{/total_discount}}
                <div class="total-row final">
                    <span>Total:</span>
                    <span>${{ total_amount }}</span>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="payment-terms">
                Payment Terms: {{ payment_terms }}
            </div>
            <div class="footer-content">
                <div class="footer-item">
                    <span class="footer-label">Bank Details</span>
                    {{ bank_name }}<br>
                    Account: {{ account_number }}<br>
                    Routing: {{ routing_number }}
                </div>
                <div class="footer-item">
                    <span class="footer-label">Tax ID</span>
                    {{ tax_id }}
                </div>
                <div class="footer-item">
                    <span class="footer-label">Business Registration</span>
                    {{ business_registration }}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
